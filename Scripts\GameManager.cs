using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;  // Used by SceneManager to change Scenes

public class GameManager : MonoBehaviour
{
    // Connected to Buttons in Inspector
    public void GoToScene(string sceneName)
    {
        // Check if 'SceneName' was set
        if (sceneName != string.Empty)
            SceneManager.LoadScene(sceneName);
        else
            Debug.LogError("sceneName has not been set. ", gameObject);
    }

    // Connected to Button_Quit in Inspector
    public void Quit()
    {
#if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
    }
}
