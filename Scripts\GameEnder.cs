using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;  // Used by SceneManager to change Scenes

[RequireComponent(typeof(Collider))]
public class GameEnder : MonoBehaviour
{
    [Tooltip("Scene name to switch to")]
    public string sceneName;

    // Usage Rules:
    // - Both GameObjects in Scene need to have colliders
    // - One or both GameObjects need to be marked as "Is Trigger"
    // - Called once when GameObjects start overlapping
    void OnTriggerEnter(Collider other)
    {
        // Check if gameObject collided with something tagged as 'Player'
        if (other.gameObject.CompareTag("Player"))
        {
            // Go to a Scene
            SceneManager.LoadScene(sceneName);
        }
    }
}
