using UnityEngine;

namespace HEL<PERSON><PERSON>IKE
{
    /// <summary>
    /// Setup Guide for the Interaction Prompt System
    /// 
    /// This system shows "E to Interact" text when the player is near interactable objects
    /// like skull pickups and pedestals in the skull puzzle system.
    /// 
    /// COMPONENTS INCLUDED:
    /// 1. InteractionPrompt.cs - Manages the UI text that shows interaction prompts
    /// 
    /// SETUP INSTRUCTIONS:
    /// 
    /// === STEP 1: CREATE THE UI TEXT ===
    /// 1. In your Canvas, create a new UI → Text - TextMeshPro
    /// 2. Name it "InteractionText"
    /// 3. Position it where you want the interaction prompt to appear (center-bottom is recommended)
    /// 4. Set the text to something like "E to Interact" (this will be overridden by the script)
    /// 5. Style the text as desired (font, size, color, etc.)
    /// 6. Make sure the text is initially visible for setup, but the script will handle visibility
    /// 
    /// === STEP 2: ADD THE INTERACTION PROMPT SCRIPT ===
    /// 1. Add the InteractionPrompt script to the TextMeshPro GameObject you just created
    /// 2. The script will automatically find the TextMeshPro component
    /// 3. Configure the settings in the inspector:
    ///    - Enable Fade Animation: Smooth fade in/out effect
    ///    - Fade Speed: How fast the text fades (5 is good default)
    ///    - Enable Scale Animation: Optional pulsing scale effect
    ///    - Auto Position: Automatically centers the text with offset
    ///    - Screen Offset: Position offset from center (0, -100 puts it below center)
    /// 
    /// === STEP 3: VERIFY SKULL PUZZLE INTEGRATION ===
    /// The skull pickup and pedestal scripts have been automatically updated to work with
    /// the interaction prompt system. Make sure you have:
    /// 1. SkullPickup scripts on your skull collectibles
    /// 2. SkullPedestal scripts on your pedestals
    /// 3. Both scripts will automatically find and use the InteractionPrompt
    /// 
    /// === HOW IT WORKS ===
    /// 
    /// Skull Pickups:
    /// - When player gets near a skull: Shows "E to Pick Up"
    /// - Only shows if player doesn't already have a skull
    /// - Automatically hides when player moves away or picks up the skull
    /// 
    /// Skull Pedestals:
    /// - When player gets near with correct skull: Shows "E to Place Skull"
    /// - When player gets near with wrong skull: Shows "Need [Color] Skull"
    /// - When player gets near without skull: Shows "Need Skull"
    /// - When player gets near pedestal with skull: Shows "E to Remove Skull"
    /// - Automatically hides when player moves away
    /// 
    /// === CUSTOMIZATION OPTIONS ===
    /// 
    /// InteractionPrompt Settings:
    /// - Fade Animation: Smooth fade in/out when showing/hiding
    /// - Scale Animation: Optional pulsing effect for attention
    /// - Auto Position: Automatically positions text on screen
    /// - Screen Offset: Fine-tune position relative to screen center
    /// - Debug: Enable console messages for troubleshooting
    /// 
    /// Text Styling:
    /// - Use TextMeshPro's rich text features for styling
    /// - Change font, size, color, outline, shadow, etc.
    /// - Position anywhere on screen by disabling Auto Position
    /// 
    /// === TROUBLESHOOTING ===
    /// 
    /// "InteractionPrompt not found" warnings:
    /// - Make sure you have created the UI text and added the InteractionPrompt script
    /// - Check that the script is on a GameObject in the scene (not a prefab)
    /// - Verify the GameObject is active in the hierarchy
    /// 
    /// Text not showing:
    /// - Check that the TextMeshPro component is assigned in the InteractionPrompt script
    /// - Verify the Canvas is set to Screen Space - Overlay
    /// - Make sure the text GameObject is active
    /// - Check the Canvas sorting order if you have multiple canvases
    /// 
    /// Text showing in wrong position:
    /// - Disable Auto Position in InteractionPrompt settings
    /// - Manually position the text using the RectTransform
    /// - Or adjust the Screen Offset values for fine-tuning
    /// 
    /// Prompts not appearing near objects:
    /// - Check that skull pickups and pedestals have their scripts attached
    /// - Verify the interaction ranges are set appropriately
    /// - Make sure the player GameObject has the "Player" tag
    /// - Check console for debug messages if Debug is enabled
    /// 
    /// === ADVANCED USAGE ===
    /// 
    /// Multiple Interaction Prompts:
    /// - You can have multiple InteractionPrompt scripts for different areas
    /// - Each skull pickup/pedestal will use the first one found in the scene
    /// - Useful for split-screen or multiple UI layouts
    /// 
    /// Custom Prompt Messages:
    /// - The skull scripts automatically generate appropriate messages
    /// - You can modify the HandleInteractionPrompt methods in SkullPickup.cs 
    ///   and SkullPedestal.cs to customize the messages
    /// 
    /// Integration with Other Systems:
    /// - Other scripts can use InteractionPrompt by calling:
    ///   FindFirstObjectByType<InteractionPrompt>().ShowPrompt("Your Message");
    /// - Remember to call HidePrompt() when the interaction is no longer available
    /// 
    /// === DEBUG FEATURES ===
    /// - Enable Debug in InteractionPrompt settings for console messages
    /// - Enable Debug in SkullPickup and SkullPedestal for interaction logging
    /// - Check Scene view gizmos to see interaction ranges
    /// 
    /// You can disable debug messages by unchecking "Debug" options in each script.
    /// </summary>
    public class InteractionPromptSetupGuide : MonoBehaviour
    {
        [Header("This is a setup guide - you can delete this component")]
        [TextArea(10, 20)]
        public string setupInstructions = "See the script comments above for detailed setup instructions!";
        
        void Start()
        {
            Debug.Log("InteractionPromptSetupGuide: Check the script comments for detailed setup instructions!");
        }
    }
}
