%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.5188679, g: 0.5188679, b: 0.5188679, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 2100000, guid: ********************************, type: 2}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 256
  m_ReflectionBounces: 3
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 705507994}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000000, guid: 44ee8773ec89e6041b06d2c51cdb81b5, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 865f7e608fbfb0342803cfb1a41c96b3, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &69356446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 69356447}
  - component: {fileID: 69356450}
  - component: {fileID: 69356449}
  - component: {fileID: 69356448}
  m_Layer: 0
  m_Name: Cube (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &69356447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69356446}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -12.366932, y: -3.5173542, z: 2.701233}
  m_LocalScale: {x: 15, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &69356448
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69356446}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &69356449
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69356446}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f0d488fa650bb04392760c9aa3aafae, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &69356450
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69356446}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &152671653
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 152671654}
  - component: {fileID: 152671657}
  - component: {fileID: 152671656}
  - component: {fileID: 152671655}
  m_Layer: 0
  m_Name: Cube (28)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &152671654
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152671653}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -2.994, z: -0.29800034}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &152671655
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152671653}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &152671656
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152671653}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &152671657
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152671653}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &165187513
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 165187514}
  - component: {fileID: 165187517}
  - component: {fileID: 165187516}
  - component: {fileID: 165187515}
  m_Layer: 0
  m_Name: Cube (25)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &165187514
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165187513}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -1.494, z: 2.7019997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &165187515
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165187513}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &165187516
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165187513}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &165187517
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165187513}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &227207491 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
  m_PrefabInstance: {fileID: 823873705}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &240539418
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 240539422}
  - component: {fileID: 240539421}
  - component: {fileID: 240539420}
  - component: {fileID: 240539419}
  m_Layer: 0
  m_Name: Cube (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &240539419
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240539418}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000004, y: 1.0000045, z: 1}
  m_Center: {x: -0.000000029802322, y: 0, z: -0.0000000059618515}
--- !u!23 &240539420
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240539418}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &240539421
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240539418}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &240539422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 240539418}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999952, y: -0.49999967, z: -0.5000004, w: 0.5000004}
  m_LocalPosition: {x: 2.6330686, y: -0.96735525, z: -4.8487797}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!1 &272361363
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 272361364}
  - component: {fileID: 272361367}
  - component: {fileID: 272361366}
  - component: {fileID: 272361365}
  m_Layer: 0
  m_Name: Cube (21)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &272361364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272361363}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -2.994, z: 3.7019997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &272361365
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272361363}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &272361366
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272361363}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &272361367
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272361363}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &296844529
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 296844530}
  - component: {fileID: 296844533}
  - component: {fileID: 296844532}
  - component: {fileID: 296844531}
  m_Layer: 0
  m_Name: Cube (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &296844530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296844529}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999952, y: -0.49999967, z: -0.5000004, w: 0.5000004}
  m_LocalPosition: {x: -12.366932, y: -0.9673533, z: 10.251245}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!65 &296844531
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296844529}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000004, y: 1.0000045, z: 1}
  m_Center: {x: 0.000000029802322, y: 0, z: 0}
--- !u!23 &296844532
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296844529}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &296844533
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 296844529}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &424878806
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 424878807}
  - component: {fileID: 424878810}
  - component: {fileID: 424878809}
  - component: {fileID: 424878808}
  m_Layer: 0
  m_Name: Cube (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &424878807
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 424878806}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49999952, y: -0.49999967, z: -0.5000004, w: 0.5000004}
  m_LocalPosition: {x: -12.366932, y: -0.96735287, z: -4.848756}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: -90}
--- !u!65 &424878808
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 424878806}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000004, y: 1.0000045, z: 1}
  m_Center: {x: -0.000000029802322, y: 0, z: -0.00000011920929}
--- !u!23 &424878809
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 424878806}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &424878810
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 424878806}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &427064848
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 427064849}
  - component: {fileID: 427064852}
  - component: {fileID: 427064851}
  - component: {fileID: 427064850}
  m_Layer: 0
  m_Name: Cube (15)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &427064849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 427064848}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.70710677, w: 0.7071068}
  m_LocalPosition: {x: -19.916931, y: -0.9673524, z: 2.7012568}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!65 &427064850
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 427064848}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0.000000029802322, y: -0.000015258789, z: -0.000000004966978}
--- !u!23 &427064851
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 427064848}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &427064852
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 427064848}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &598088166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 598088167}
  - component: {fileID: 598088170}
  - component: {fileID: 598088169}
  - component: {fileID: 598088168}
  m_Layer: 0
  m_Name: Cube (14)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &598088167
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 598088166}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -9.866932, y: -2.971, z: 4.701233}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &598088168
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 598088166}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &598088169
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 598088166}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &598088170
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 598088166}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &639168449
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 639168453}
  - component: {fileID: 639168452}
  - component: {fileID: 639168451}
  - component: {fileID: 639168450}
  m_Layer: 0
  m_Name: Cube (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &639168450
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 639168449}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000005, y: 1.0000209, z: 1}
  m_Center: {x: -0.000000029802322, y: 0, z: 0.00000000755}
--- !u!23 &639168451
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 639168449}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &639168452
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 639168449}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &639168453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 639168449}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999997, y: 0.49999997, z: -0.50000006, w: 0.5000001}
  m_LocalPosition: {x: 2.6330786, y: -0.9673526, z: 10.251234}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: -90}
--- !u!1 &705121508
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 705121512}
  - component: {fileID: 705121511}
  - component: {fileID: 705121510}
  - component: {fileID: 705121509}
  m_Layer: 0
  m_Name: Cube (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &705121509
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705121508}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &705121510
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705121508}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &705121511
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705121508}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &705121512
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705121508}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.2330785, y: -2.4673524, z: -3.7987652}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &705507993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 705507995}
  - component: {fileID: 705507994}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &705507994
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705507993}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 1
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 1e-45, z: 9.326e-42, w: -6.164622e+30}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &705507995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705507993}
  serializedVersion: 2
  m_LocalRotation: {x: 0.29858565, y: -0.64097315, z: 0.29858565, w: 0.64097315}
  m_LocalPosition: {x: 19.94, y: 8.13, z: -1.39}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 49.955, y: -90, z: 0}
--- !u!1 &799385574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 799385575}
  - component: {fileID: 799385576}
  m_Layer: 0
  m_Name: Enviroment
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &799385575
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 799385574}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.633068, y: 3.0173542, z: -2.701233}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 598088167}
  - {fileID: 1662616652}
  - {fileID: 1848810053}
  - {fileID: 705121512}
  - {fileID: 1314466044}
  - {fileID: 1784908927}
  - {fileID: 69356447}
  - {fileID: 2002927912}
  - {fileID: 427064849}
  - {fileID: 639168453}
  - {fileID: 240539422}
  - {fileID: 424878807}
  - {fileID: 296844530}
  - {fileID: 1995670035}
  - {fileID: 1836896914}
  - {fileID: 967632100}
  - {fileID: 272361364}
  - {fileID: 1018443025}
  - {fileID: 1015775092}
  - {fileID: 1398975931}
  - {fileID: 165187514}
  - {fileID: 1010201337}
  - {fileID: 1387013736}
  - {fileID: 152671654}
  - {fileID: 1228592996}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &799385576
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 799385574}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a5ac11cc976e418e8d13136b07e1f52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_AgentTypeID: 0
  m_CollectObjects: 0
  m_Size: {x: 10, y: 10, z: 10}
  m_Center: {x: 0, y: 2, z: 0}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_UseGeometry: 0
  m_DefaultArea: 0
  m_GenerateLinks: 0
  m_IgnoreNavMeshAgent: 1
  m_IgnoreNavMeshObstacle: 1
  m_OverrideTileSize: 0
  m_TileSize: 256
  m_OverrideVoxelSize: 0
  m_VoxelSize: 0.16666667
  m_MinRegionArea: 2
  m_NavMeshData: {fileID: 23800000, guid: a534726e4d9c67a4a87ec827352535b5, type: 2}
  m_BuildHeightMesh: 0
--- !u!1001 &823873705
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1490822070}
    m_Modifications:
    - target: {fileID: 3264794168526244036, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_Name
      value: ExplodingSpider (Non-Stationary)
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalPosition.x
      value: -15.465126
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.8757067
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.8363514
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.070446245
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.99751556
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -171.921
      objectReference: {fileID: 0}
    - target: {fileID: 7442659691892121631, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d6c4d4e660739f54aa703c179c13dff5, type: 3}
--- !u!1 &967632099
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 967632100}
  - component: {fileID: 967632103}
  - component: {fileID: 967632102}
  - component: {fileID: 967632101}
  m_Layer: 0
  m_Name: Cube (18)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &967632100
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967632099}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -11.366932, y: -2.5673542, z: -0.5487671}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &967632101
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967632099}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &967632102
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967632099}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &967632103
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967632099}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1010201336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1010201337}
  - component: {fileID: 1010201340}
  - component: {fileID: 1010201339}
  - component: {fileID: 1010201338}
  m_Layer: 0
  m_Name: Cube (26)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1010201337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010201336}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -0.99399996, z: -0.29800034}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1010201338
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010201336}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1010201339
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010201336}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1010201340
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010201336}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1015775091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1015775092}
  - component: {fileID: 1015775095}
  - component: {fileID: 1015775094}
  - component: {fileID: 1015775093}
  m_Layer: 0
  m_Name: Cube (23)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1015775092
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015775091}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -0.99399996, z: 3.7019997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1015775093
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015775091}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1015775094
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015775091}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1015775095
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015775091}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1018443024
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1018443025}
  - component: {fileID: 1018443028}
  - component: {fileID: 1018443027}
  - component: {fileID: 1018443026}
  m_Layer: 0
  m_Name: Cube (22)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1018443025
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018443024}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -1.994, z: 3.7019997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1018443026
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018443024}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1018443027
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018443024}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1018443028
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018443024}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1070096155
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1070096157}
  - component: {fileID: 1070096156}
  m_Layer: 0
  m_Name: Reflection Probe
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!215 &1070096156
ReflectionProbe:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070096155}
  m_Enabled: 1
  serializedVersion: 2
  m_Type: 0
  m_Mode: 0
  m_RefreshMode: 0
  m_TimeSlicingMode: 0
  m_Resolution: 512
  m_UpdateFrequency: 0
  m_BoxSize: {x: 15.018217, y: 10, z: 16.913568}
  m_BoxOffset: {x: 1.2287645, y: 0, z: 2.1982396}
  m_NearClip: 0.3
  m_FarClip: 1000
  m_ShadowDistance: 100
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IntensityMultiplier: 1
  m_BlendDistance: 1
  m_HDR: 1
  m_BoxProjection: 0
  m_RenderDynamicObjects: 0
  m_UseOcclusionCulling: 1
  m_Importance: 1
  m_CustomBakedTexture: {fileID: 0}
--- !u!4 &1070096157
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070096155}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.7147479, y: 3.43, z: -2.539741}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1228592995
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1228592996}
  - component: {fileID: 1228592999}
  - component: {fileID: 1228592998}
  - component: {fileID: 1228592997}
  m_Layer: 0
  m_Name: Cube (29)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1228592996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228592995}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -1.994, z: -0.29800034}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1228592997
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228592995}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1228592998
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228592995}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1228592999
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228592995}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1314466040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1314466044}
  - component: {fileID: 1314466043}
  - component: {fileID: 1314466042}
  - component: {fileID: 1314466041}
  m_Layer: 0
  m_Name: Cube (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &1314466041
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314466040}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1314466042
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314466040}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1314466043
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314466040}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1314466044
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314466040}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.633068, y: -0.47000003, z: 7.201233}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1387013735
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1387013736}
  - component: {fileID: 1387013739}
  - component: {fileID: 1387013738}
  - component: {fileID: 1387013737}
  m_Layer: 0
  m_Name: Cube (27)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1387013736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1387013735}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -1.494, z: 0.70199966}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1387013737
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1387013735}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1387013738
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1387013735}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1387013739
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1387013735}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1398975930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1398975931}
  - component: {fileID: 1398975934}
  - component: {fileID: 1398975933}
  - component: {fileID: 1398975932}
  m_Layer: 0
  m_Name: Cube (24)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1398975931
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398975930}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3299999, y: -1.494, z: 1.7019997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1398975932
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398975930}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1398975933
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398975930}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1398975934
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398975930}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1490822069
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1490822070}
  m_Layer: 0
  m_Name: Obstacles
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1490822070
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1490822069}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.7748735, y: 2.4257069, z: -0.56364864}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 227207491}
  - {fileID: 1775663256700757609}
  - {fileID: 2008548446}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1662616648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1662616652}
  - component: {fileID: 1662616651}
  - component: {fileID: 1662616650}
  - component: {fileID: 1662616649}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &1662616649
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662616648}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1662616650
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662616648}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1662616651
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662616648}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1662616652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662616648}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.633068, y: -2.47, z: 7.201233}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1784908923
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1784908927}
  - component: {fileID: 1784908926}
  - component: {fileID: 1784908925}
  - component: {fileID: 1784908924}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &1784908924
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784908923}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1784908925
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784908923}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4f0d488fa650bb04392760c9aa3aafae, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1784908926
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784908923}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1784908927
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784908923}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.633068, y: -3.5173542, z: 2.701233}
  m_LocalScale: {x: 15, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1836896913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1836896914}
  - component: {fileID: 1836896917}
  - component: {fileID: 1836896916}
  - component: {fileID: 1836896915}
  m_Layer: 0
  m_Name: Cube (13)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1836896914
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1836896913}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -11.366932, y: -2.471, z: 4.201233}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1836896915
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1836896913}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1836896916
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1836896913}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1836896917
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1836896913}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1848810049
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1848810053}
  - component: {fileID: 1848810052}
  - component: {fileID: 1848810051}
  - component: {fileID: 1848810050}
  m_Layer: 0
  m_Name: Cube (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &1848810050
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848810049}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1848810051
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848810049}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1848810052
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848810049}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1848810053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848810049}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 9.233078, y: -2.4673524, z: -3.7987652}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1995670034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1995670035}
  - component: {fileID: 1995670038}
  - component: {fileID: 1995670037}
  - component: {fileID: 1995670036}
  m_Layer: 0
  m_Name: Cube (12)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!4 &1995670035
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995670034}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 4.633068, y: -2.47, z: 7.201233}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1995670036
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995670034}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1995670037
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995670034}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 89c4b9b4900beb144947c7873c6e651f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1995670038
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995670034}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &2002927908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2002927912}
  - component: {fileID: 2002927911}
  - component: {fileID: 2002927910}
  - component: {fileID: 2002927909}
  m_Layer: 0
  m_Name: Cube (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 2147483647
  m_IsActive: 1
--- !u!65 &2002927909
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002927908}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0.000000029802322, y: 0, z: -0.0000000069538686}
--- !u!23 &2002927910
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002927908}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7712aababe6a35542a4207483e87520f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 1
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &2002927911
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002927908}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &2002927912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2002927908}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.70710677, w: 0.7071068}
  m_LocalPosition: {x: 10.183079, y: -0.9673524, z: 2.7012348}
  m_LocalScale: {x: 5, y: 0.1, z: 15}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 799385575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!4 &2008548446 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
  m_PrefabInstance: {fileID: 6544541826543370389}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &543450652366427798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 919132148381846572}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 16.321087, y: 0.8646767, z: -3.5795412}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9026837973728442734}
  - {fileID: 5006429691348475864}
  - {fileID: 1797658346004689211}
  - {fileID: 7965762304419531725}
  - {fileID: 8977062828235956853}
  - {fileID: 7456838843422697304}
  - {fileID: 3399536108698799003}
  - {fileID: 6280411546445590484}
  - {fileID: 6052707828318972071}
  - {fileID: 6166615021879799495}
  - {fileID: 2364220375318470031}
  - {fileID: 7179120529802417704}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &919132148381846572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 543450652366427798}
  m_Layer: 0
  m_Name: psx-eldritch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1001 &1512378493244612760
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.0182942
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.18631427
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.6725391
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8894323378065855472, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
      propertyPath: m_Name
      value: Creature_10
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
--- !u!1001 &1550033275154484129
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 1450643360033774619, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_Name
      value: Creature_8
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.317873
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.49388766
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalPosition.z
      value: 12.547757
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
--- !u!4 &1775663256700757609 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
  m_PrefabInstance: {fileID: 7054067420194460387}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1797658346004689211 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
  m_PrefabInstance: {fileID: 5406090551650941199}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2037068997962885779
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.16421852
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.67203945
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalPosition.z
      value: 10.074748
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4300806899136637831, guid: efa90818247733648a1dc20c8285f535, type: 3}
      propertyPath: m_Name
      value: Creature_13
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: efa90818247733648a1dc20c8285f535, type: 3}
--- !u!4 &2364220375318470031 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
  m_PrefabInstance: {fileID: 4328923779968178526}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3165860934456232273
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.119254
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.3907837
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.5432321
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8954429219444385367, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
      propertyPath: m_Name
      value: Creature_5_var_2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
--- !u!4 &3399536108698799003 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4111288378473622844, guid: 6d5686458d0778c4884c2e48f61f6572, type: 3}
  m_PrefabInstance: {fileID: 1550033275154484129}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4034171850319218216
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.3806834
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.066846356
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalPosition.z
      value: 8.478139
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8472513093825826420, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
      propertyPath: m_Name
      value: Creature_7
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
--- !u!1001 &4328923779968178526
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.04786377
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.4788598
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.8547363
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2030512914903452174, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6907192753909671009, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
      propertyPath: m_Name
      value: Creature_12
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 354b0269ac6b120428a735859ec820c4, type: 3}
--- !u!4 &5006429691348475864 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
  m_PrefabInstance: {fileID: 5545666556497668975}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5156245031063514043
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.36751005
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.09678108
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalPosition.z
      value: 7.561372
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6591050980042789422, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
      propertyPath: m_Name
      value: Creature_1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
--- !u!1001 &5279658321931723655
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.2012196
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.00977133
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.3199577
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8981809337644062425, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
      propertyPath: m_Name
      value: Creature_6
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
--- !u!1001 &5406090551650941199
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.119254
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.3907837
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.7667904
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2192853340334616893, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4879563899218964938, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
      propertyPath: m_Name
      value: Creature_5
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2a3f6cef6b94d104497064c094a41950, type: 3}
--- !u!1001 &5545666556497668975
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 979838214517626406, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_Name
      value: Creature_3
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.0818734
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.7318547
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.9053984
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6085583180675553690, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 93beb2477664a694899f357f3f7876e7, type: 3}
--- !u!4 &6052707828318972071 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 572252323388207602, guid: 9aaf3bafe979b4d48a2c60d7c29412c9, type: 3}
  m_PrefabInstance: {fileID: 1512378493244612760}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6166615021879799495 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
  m_PrefabInstance: {fileID: 6516494090284103162}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6280411546445590484 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
  m_PrefabInstance: {fileID: 8701241934689747904}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6516494090284103162
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.6544966
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.37179497
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalPosition.z
      value: 2.0191631
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4139958498205420448, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4347729664012085287, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
      propertyPath: m_Name
      value: Creature_11
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: ec1dbed55ab7a284f9377be9c85a5268, type: 3}
--- !u!1001 &6544541826543370389
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1490822070}
    m_Modifications:
    - target: {fileID: 2349605394713388484, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_Name
      value: Spiketrap (Stationary,Animated)
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.7748735
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.7457068
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.32364863
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3111281730775448909, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b53bd4aa28f89d94db723fc1a6c01813, type: 3}
--- !u!1001 &7054067420194460387
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1490822070}
    m_Modifications:
    - target: {fileID: 5097549982603003057, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_Name
      value: Tentacles (Stationary)
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalPosition.x
      value: 4.228707
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.7823668
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalPosition.z
      value: 1.5450965
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5247268506785355476, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 99ddc981c5911274ea2a2592df1ce6e4, type: 3}
--- !u!4 &7179120529802417704 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4223448013902191397, guid: efa90818247733648a1dc20c8285f535, type: 3}
  m_PrefabInstance: {fileID: 2037068997962885779}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7456838843422697304 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6193395838885906723, guid: d8ef3d032397b7f45ae133abb77e88d9, type: 3}
  m_PrefabInstance: {fileID: 4034171850319218216}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7965762304419531725 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7479968389301580919, guid: 272abafc26e1eb64ab8139d460f87ade, type: 3}
  m_PrefabInstance: {fileID: 3165860934456232273}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8701241934689747904
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 543450652366427798}
    m_Modifications:
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.00383777
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.13871878
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalPosition.z
      value: -3.566453
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 87979681758000809, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4052904826586198236, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
      propertyPath: m_Name
      value: Creature_9
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 174e1d9c5b4505a41bfe823df2feb560, type: 3}
--- !u!4 &8977062828235956853 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2576345927627897820, guid: 4bcc56511022ec0469fc48ef612193b5, type: 3}
  m_PrefabInstance: {fileID: 5279658321931723655}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &9026837973728442734 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4251662446945693840, guid: 0e0b9ef80861c32488b5f69a774e3391, type: 3}
  m_PrefabInstance: {fileID: 5156245031063514043}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &9156347143826488031
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4994268985960309687, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: enableHealthRegen
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.435
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.99
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445546, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9156347144040445547, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
      propertyPath: m_Name
      value: FirstPersonController
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c82236c16aae9d34fbd437f8dea6e2c9, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 705507995}
  - {fileID: 799385575}
  - {fileID: 1070096157}
  - {fileID: 9156347143826488031}
  - {fileID: 1490822070}
  - {fileID: 543450652366427798}
