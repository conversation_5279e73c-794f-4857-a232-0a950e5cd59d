using UnityEngine;
using T<PERSON>ro;

namespace HELLSTRIKE
{
    /// <summary>
    /// Manages the interaction prompt UI that shows "E to Interact" text when near interactable objects.
    /// This script should be attached to a TextMeshPro UI element.
    /// </summary>
    public class InteractionPrompt : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private TextMeshProUGUI promptText;
        [SerializeField] private bool autoFindPromptText = true;
        [SerializeField] private string promptTextName = "InteractionText"; // Name to search for if auto-finding
        
        [Header("Settings")]
        [SerializeField] private bool enableFadeAnimation = true;
        [SerializeField] private float fadeSpeed = 5f;
        [SerializeField] private bool enableScaleAnimation = false;
        [SerializeField] private float scaleSpeed = 8f;
        [SerializeField] private float scaleAmount = 0.1f;
        
        [Header("Positioning")]
        [SerializeField] private bool autoPosition = true;
        [SerializeField] private Vector2 screenOffset = new Vector2(0, -100); // Offset from center of screen
        
        [Header("Debug")]
        [SerializeField] private bool debugPrompt = true;
        
        // Private variables
        private bool isVisible = false;
        private bool isAnimating = false;
        private float animationTimer = 0f;
        private CanvasGroup canvasGroup;
        private RectTransform rectTransform;
        private Vector3 originalScale;
        private string currentPromptText = "";
        
        void Start()
        {
            // Auto-find prompt text if enabled
            if (autoFindPromptText && promptText == null)
            {
                // First try to find by name
                GameObject textObj = GameObject.Find(promptTextName);
                if (textObj != null)
                {
                    promptText = textObj.GetComponent<TextMeshProUGUI>();
                }
                
                // If not found by name, try to find any TextMeshProUGUI in children
                if (promptText == null)
                {
                    promptText = GetComponentInChildren<TextMeshProUGUI>();
                }
                
                // If still not found, try to get from this GameObject
                if (promptText == null)
                {
                    promptText = GetComponent<TextMeshProUGUI>();
                }
                
                if (promptText == null && debugPrompt)
                {
                    Debug.LogWarning("InteractionPrompt: No TextMeshProUGUI found! Please assign one manually.");
                }
            }
            
            // Setup components
            if (promptText != null)
            {
                rectTransform = promptText.GetComponent<RectTransform>();
                canvasGroup = promptText.GetComponent<CanvasGroup>();
                
                if (canvasGroup == null)
                {
                    canvasGroup = promptText.gameObject.AddComponent<CanvasGroup>();
                }
                
                originalScale = rectTransform.localScale;
                
                // Auto-position if enabled
                if (autoPosition)
                {
                    PositionPrompt();
                }
                
                // Start hidden
                HidePrompt();
            }
            
            if (debugPrompt)
            {
                Debug.Log($"InteractionPrompt: Initialized at {transform.position}");
            }
        }
        
        void Update()
        {
            if (isAnimating)
            {
                HandleAnimation();
            }
        }
        
        private void PositionPrompt()
        {
            if (rectTransform == null) return;
            
            // Position in center of screen with offset
            rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
            rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
            rectTransform.anchoredPosition = screenOffset;
            
            if (debugPrompt)
            {
                Debug.Log($"InteractionPrompt: Auto-positioned to {rectTransform.anchoredPosition}");
            }
        }
        
        public void ShowPrompt(string text)
        {
            if (promptText == null) return;
            
            currentPromptText = text;
            promptText.text = text;
            isVisible = true;
            
            // Show the text
            promptText.gameObject.SetActive(true);
            
            // Start animation
            if (enableFadeAnimation || enableScaleAnimation)
            {
                StartShowAnimation();
            }
            else
            {
                // Set directly without animation
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = 1f;
                }
            }
            
            if (debugPrompt)
            {
                Debug.Log($"InteractionPrompt: Showing prompt: {text}");
            }
        }
        
        public void HidePrompt()
        {
            if (promptText == null) return;
            
            isVisible = false;
            
            // Start hide animation or hide directly
            if (enableFadeAnimation || enableScaleAnimation)
            {
                StartHideAnimation();
            }
            else
            {
                // Hide directly without animation
                promptText.gameObject.SetActive(false);
                if (canvasGroup != null)
                {
                    canvasGroup.alpha = 0f;
                }
            }
            
            if (debugPrompt)
            {
                Debug.Log("InteractionPrompt: Hiding prompt");
            }
        }
        
        private void StartShowAnimation()
        {
            if (!enableFadeAnimation && !enableScaleAnimation) return;
            
            isAnimating = true;
            animationTimer = 0f;
            
            // Reset scale
            if (rectTransform != null)
            {
                rectTransform.localScale = originalScale;
            }
        }
        
        private void StartHideAnimation()
        {
            if (!enableFadeAnimation && !enableScaleAnimation) return;
            
            isAnimating = true;
            animationTimer = 0f;
        }
        
        private void HandleAnimation()
        {
            animationTimer += Time.deltaTime;
            
            if (isVisible)
            {
                // Show animation
                if (enableFadeAnimation && canvasGroup != null)
                {
                    float targetAlpha = 1f;
                    canvasGroup.alpha = Mathf.Lerp(canvasGroup.alpha, targetAlpha, fadeSpeed * Time.deltaTime);
                    
                    if (Mathf.Abs(canvasGroup.alpha - targetAlpha) < 0.01f)
                    {
                        canvasGroup.alpha = targetAlpha;
                    }
                }
                
                if (enableScaleAnimation && rectTransform != null)
                {
                    float scaleMultiplier = 1f + Mathf.Sin(animationTimer * scaleSpeed) * scaleAmount;
                    rectTransform.localScale = originalScale * scaleMultiplier;
                }
                
                // End show animation after fade is complete
                if (!enableScaleAnimation && canvasGroup != null && canvasGroup.alpha >= 0.99f)
                {
                    isAnimating = false;
                }
            }
            else
            {
                // Hide animation
                if (enableFadeAnimation && canvasGroup != null)
                {
                    float targetAlpha = 0f;
                    canvasGroup.alpha = Mathf.Lerp(canvasGroup.alpha, targetAlpha, fadeSpeed * Time.deltaTime);
                    
                    if (canvasGroup.alpha <= 0.01f)
                    {
                        canvasGroup.alpha = targetAlpha;
                        promptText.gameObject.SetActive(false);
                        isAnimating = false;
                        
                        // Reset scale
                        if (rectTransform != null)
                        {
                            rectTransform.localScale = originalScale;
                        }
                    }
                }
                else
                {
                    // No fade animation, just hide immediately
                    promptText.gameObject.SetActive(false);
                    isAnimating = false;
                    
                    // Reset scale
                    if (rectTransform != null)
                    {
                        rectTransform.localScale = originalScale;
                    }
                }
            }
        }
        
        // Public methods for external control
        public void SetPromptText(TextMeshProUGUI newPromptText)
        {
            promptText = newPromptText;
            if (promptText != null)
            {
                rectTransform = promptText.GetComponent<RectTransform>();
                canvasGroup = promptText.GetComponent<CanvasGroup>();
                
                if (canvasGroup == null)
                {
                    canvasGroup = promptText.gameObject.AddComponent<CanvasGroup>();
                }
                
                originalScale = rectTransform.localScale;
            }
        }
        
        public void UpdatePromptText(string newText)
        {
            if (isVisible && promptText != null)
            {
                currentPromptText = newText;
                promptText.text = newText;
            }
        }
        
        public void ForceRefresh()
        {
            if (isVisible)
            {
                ShowPrompt(currentPromptText);
            }
            else
            {
                HidePrompt();
            }
        }
        
        // Properties
        public bool IsVisible => isVisible;
        public string CurrentPromptText => currentPromptText;
        public TextMeshProUGUI PromptText => promptText;
    }
}
